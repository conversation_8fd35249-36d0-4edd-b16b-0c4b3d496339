# 用户说明文档

本文档旨在向用户提供关于我们所提供服务的全面说明，帮助您更好地理解和使用我们的解决方案。

## 一、核心网络互通服务

### 1. IPv4客户端通过IPv6网络访问IPv4-Internet资源服务

此服务允许您的IPv4客户端设备通过我们的IPv6网络基础设施，无缝访问位于IPv4互联网上的各类资源。这意味着即使您的客户端是IPv4环境，也能享受IPv6网络带来的优势，同时不影响对现有IPv4资源的访问能力。

### 2. IPv4客户端直接访问IPv4网络资源服务

我们提供服务，确保IPv4客户端能够直接、高效地访问其所在的IPv4网络资源。这保证了在纯IPv4环境下的网络通信畅通无阻，满足传统网络的连接需求。

### 3. IPv4/IPv6双栈客户端网络访问服务

对于具备IPv4/IPv6双栈能力的客户端，我们提供全面的网络访问服务。这意味着您的双栈设备既可以访问IPv4网络资源，也能同时访问IPv6网络资源，实现最大化的网络兼容性和灵活性。

### 4. 特殊需求及条件下IPv6网络访问IPv4-Internet资源服务

在特定或特殊需求场景下，我们能够实现IPv6网络对IPv4互联网资源的访问。此服务针对复杂网络环境或特定业务需求，提供定制化的IPv6到IPv4转换解决方案，确保关键业务的连续性。

## 二、配套与支持服务

我们提供一系列全面的配套服务，确保您的网络部署和运营顺利进行：

*   **现场勘查服务**：专业团队进行现场网络环境评估。
*   **专业整改设计**：根据勘查结果提供定制化的网络整改方案。
*   **机房综合布线服务**：提供标准化、高效率的机房布线解决方案。
*   **整改服务**：实施网络整改设计方案，优化网络基础设施。
*   **系统上线**：协助您完成新系统的部署和上线工作。
*   **网络对接项目整体联调及测试**：确保新旧系统或不同网络间的无缝对接与稳定运行。
*   **机房线路梳理**：对现有机房线路进行整理和优化，提升可维护性。

## 三、核心功能服务

### 1. 网络层功能服务

我们的网络层服务提供以下核心能力：

*   **协议支持**：全面支持TCP、UDP等主流传输层协议，确保数据传输的可靠性和效率。
*   **应用层协议透传**：支持大多数应用层协议的透明传输，确保各类应用程序的正常运行。
*   **基于源地址的会话保持**：提供基于源地址的会话保持功能，确保同一客户端的后续请求被分发到相同的后端服务器，维持会话连续性。
*   **健康检查**：支持Ping、TCP、Http、Https、SMTP等多种协议的健康检查机制，实时监控后端服务的可用性。

### 2. 应用层功能服务

*   **内嵌地址转换**：系统内置地址转换功能，提供特定网址的IPv4地址侦测和转换服务。
*   **IPv4到IPv6地址转换**：将侦测到的IPv4地址转换为IPv6地址，以便IPv6一次转换用户能够顺利访问IPv4资源。

### 3. 访问控制功能服务

通过灵活配置访问控制列表（ACL），我们能有效限制网络数据流，从而显著提高网络性能。例如，在公司网络中配置ACL限制传输视频数据流，可以有效降低网络负载并提升整体网络性能。

### 4. 双热备功能服务

系统具备高可用性设计，当因内部或外部原因发生故障时，能够及时自动切换启用备用系统，确保业务连续性，并自动发送告警邮件通知相关人员。

### 5. 会话保持功能服务

作为负载均衡器上的一种关键机制，会话保持功能能够识别客户端与系统交互过程中的关联性。在进行负载均衡的同时，确保一系列相关联的访问请求能够被分配到同一后端系统上，避免会话中断或数据不一致。

### 6. 管理功能服务

我们的转换系统流量调度管理平台提供直观的Web可视化管理界面，具备以下功能：

*   **系统状态实时监控**：实时查看系统运行状态。
*   **故障告警**：在故障发生时及时发出告警。
*   **流量监控**：实时监控网络流量。
*   **流量历史数据**：查看和分析历史流量数据。
*   **IPv4和IPv6转换记录与存储**：详细记录和存储所有IPv4和IPv6转换日志。
*   **日志查询**：提供便捷的日志查询功能。

### 7. ACL规则过滤服务

此服务针对用户地址进行精细化过滤，限制非允许地址的访问。可广泛应用于ISP地址控制、目的地址黑白名单设置等场景，有效规避危险IP地址段的访问，增强网络安全性。

### 8. 资源控制服务

我们的资源控制实现思想如下：

*   **初始资源分配**：按照初始设置，为服务分配总量CPU和内存资源。
*   **隧道资源分配**：将总资源定量分配至各个隧道。
*   **动态资源调整**：根据实时流量情况，动态增加或删减隧道的CPU和内存资源。
*   **性能维持**：在动态调整的同时，维持系统的通过时延和CPU占用率在最优状态。
*   **预留资源**：提供动态添加隧道所需的预留资源。

### 9. 路由管理服务

通过预设的路由方向，系统能够将各IP地址段经过逻辑判定和优化后，自动绑定到相应的隧道。例如，可以将去往东北方向的流量智能指向北京核心节点，实现流量的优化分发和管理。

## 四、物理接口层服务

### 1. 链路速率匹配

实现网络链路速率的自我协商能力，并支持指定链路的适配，确保最佳的物理层连接性能。

### 2. 链路聚合绑定匹配

提供链路聚合和绑定功能，可以捆绑多条物理链路，增加带宽和冗余。同时针对聚合链路进行资源调整和合理分配，优化网络吞吐量。

### 3. 物理模块适配和识别

系统能够智能识别各种物理模块，并进行相应的适配和速率下发等功能，确保硬件兼容性和最优性能。

### 4. 链路告警功能

实时判定网络链路状态，在链路发生故障时能够立即发现并发出告警通知，帮助您及时响应和解决网络问题。

## 五、其他专业服务

*   **系统集成服务**：提供全面的系统集成解决方案，确保不同系统间的协同工作。
*   **技术支撑及咨询服务**：提供专业的技术支持和咨询服务，解决您在网络部署和运营中遇到的问题。
*   **专业专项培训服务**：针对特定技术或产品，提供专业的培训课程，提升您的团队能力。 