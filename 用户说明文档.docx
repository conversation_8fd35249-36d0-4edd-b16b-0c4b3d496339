# 用户说明文档

## 1. 简介
本系统为用户提供多种基于IPv4与IPv6网络环境下的互通与资源访问服务，涵盖网络层、应用层、访问控制、资源管理、物理接口等多方面功能，满足不同场景下的网络接入与管理需求。

## 2. 服务功能说明

### 2.1 IPv4/IPv6互通服务
- 向IPv4客户端提供通过IPv6网络访问IPv4-Internet资源的服务。
- 向IPv4客户端提供直接访问IPv4网络资源的服务。
- 向IPv4/IPv6双栈客户端提供同时访问IPv4和IPv6网络的服务。
- 针对特殊需求及条件，支持IPv6网络访问IPv4-Internet资源。

### 2.2 网络层功能服务
- 支持TCP、UDP等协议。
- 支持大多数应用层协议的透传。
- 支持基于源地址的会话保持功能。
- 支持Ping、TCP、HTTP、HTTPS、SMTP等协议的健康检查。

### 2.3 应用层功能服务
- 内嵌地址转换，侦测并转换特定网址的IPv4地址为IPv6地址，供IPv6用户访问。

### 2.4 访问控制功能服务
- 限制网络数据流，提高网络性能。
- 支持配置ACL，限制视频数据流，降低网络负载。

### 2.5 双热备功能服务
- 系统故障时可自动切换至备用系统，并发送报警邮件。

### 2.6 会话保持功能服务
- 负载均衡器识别客户端会话，保证相关请求分配至同一系统。

### 2.7 管理功能服务
- 提供Web可视化管理界面，支持系统状态监控、告警、流量监控与历史数据查询。
- 记录并存储IPv4/IPv6转换日志，支持日志查询。

### 2.8 ACL规则过滤服务
- 针对用户地址过滤，限制非允许地址访问。
- 支持ISP地址控制、黑白名单、危险IP规避等功能。

### 2.9 资源控制服务
- 按初始设置分配CPU和内存资源，动态调整以维持系统性能。
- 支持动态添加隧道的预留资源。

### 2.10 路由管理服务
- 预设路由方向，自动绑定IP地址段至各隧道，实现流量优化。

### 2.11 物理接口层服务
- 链路速率匹配：自协商或指定链路速率。
- 链路聚合绑定：实现链路聚合与资源分配。
- 物理模块适配与识别：识别并适配物理模块，速率下发。
- 链路告警：实时监控链路状态，故障时告警。

### 2.12 配套服务
- 现场勘查、整改设计、机房布线、系统上线、网络对接、线路梳理等。
- 系统集成、技术支撑、咨询与专项培训服务。

## 3. 使用方法
请根据实际需求联系技术支持团队获取详细部署与使用指导。

## 4. 注意事项
- 请确保网络环境符合系统部署要求。
- 建议定期检查系统状态与日志，及时处理告警信息。

## 5. 联系与支持
如需技术支持或咨询，请联系系统服务提供方。 